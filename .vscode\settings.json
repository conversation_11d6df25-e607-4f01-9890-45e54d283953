{"[python]": {"editor.formatOnSave": true, "editor.defaultFormatter": "charliermarsh.ruff", "editor.codeActionsOnSave": {"source.fixAll": "always", "source.organizeImports": "always"}}, "python.analysis.inlayHints.variableTypes": true, "python.analysis.inlayHints.pytestParameters": true, "python.analysis.inlayHints.functionReturnTypes": true, "python.analysis.inlayHints.callArgumentNames": "all", "python.analysis.extraPaths": ["./.venv/Lib/site-packages"], "python.analysis.autoFormatStrings": true, "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true}