{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"..\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from src.pytaifex import TTB, QuoteData, OrderSide, TimeInForce, OrderType"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# import logging\n", "# logger = logging.getLogger(\"client\")\n", "# logger.setLevel(logging.DEBUG)\n", "# handler = logging.StreamHandler()\n", "# handler.setFormatter(logging.Formatter(\"%(levelname)s [%(name)s]: %(message)s\"))\n", "# logger.add<PERSON><PERSON><PERSON>(handler)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO [TTB_3071058977728]: Initializing wrapper of TTB\n", "INFO [TTB_3071058977728]: Starting worker process.\n", "INFO [TTB_3071058977728]: Worker process started. PID: 25000\n", "INFO [TTB_3071058977728.TTBWorker]: TTB Worker started (PID: 25000), logging to main process.\n", "INFO [TTB_3071058977728.TTBWorker]: Successfully loaded TTBHelp from '../local/TTBHelp.pyc'\n", "INFO [TTB_3071058977728.TTBWorker]: TTBProcessInternal initialized with host: http://localhost:8080, zmq_port: 51141\n", "INFO [TTB_3071058977728]: TTB wrapper initialized.\n", "INFO [TTB_3071058977728.TTBWorker]: Internal TTB instance initialized and running.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO [TTB_3071058977728.TTBWorker]: Received get accounts command.\n", "INFO [TTB_3071058977728.TTBWorker]: Received get accounts command.\n", "INFO [TTB_3071058977728.TTBWorker]: Received get accounts command.\n", "INFO [TTB_3071058977728.TTBWorker]: Received get accounts command.\n", "INFO [TTB_3071058977728.TTBWorker]: Received get accounts command.\n"]}], "source": ["client = TTB(\"../local/TTBHelp.pyc\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["client.is_worker_alive()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO [TTB_1764446842432]: Creating order for TXFF5 at price 20000 with quantity 3.\n", "INFO [TTB_1764446842432]: Order created successfully.\n"]}], "source": ["client.create_order(\n", "    \"TXFF5\",\n", "    OrderSide.BUY,\n", "    \"20000\",\n", "    TimeInForce.ROD,\n", "    OrderType.LIMIT,\n", "    \"3\",\n", "    False,\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO [TTB_1756551786720]: Querying orders.\n", "INFO [TTB_1756551786720]: Orders queried successfully.\n"]}], "source": ["orders = client.get_orders()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["orders\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO [TTB_1764446842432]: Querying orders.\n", "INFO [TTB_1764446842432]: Orders queried successfully.\n"]}, {"data": {"text/plain": ["[OrderData(CX15112a, TXFF5, 刪單成功, OrderSide.BUY, 16:21:54),\n", " OrderData(CX15105a, TXFF5, 刪單成功, OrderSide.BUY, 16:21:57),\n", " OrderData(CX15104a, TXFF5, 刪單成功, OrderSide.BUY, 16:16:44),\n", " OrderData(CX15088a, TXFF5, 刪單成功, OrderSide.BUY, 16:17:21),\n", " OrderData(CX15087a, TXFF5, 刪單成功, OrderSide.BUY, 16:17:21),\n", " OrderData(CX15086a, TXFF5, 刪單成功, OrderSide.BUY, 16:17:21),\n", " OrderData(CX15029a, TXFF5, 刪單成功, OrderSide.BUY, 16:17:26),\n", " OrderData(CX15028a, TXFF5, 刪單成功, OrderSide.BUY, 16:17:26),\n", " OrderData(CX15027a, TXFF5, 刪單成功, OrderSide.BUY, 14:58:28),\n", " OrderData(CX15114a, TXFF5, 委託成功, OrderSide.BUY, 16:27:53)]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["orders_all = client.get_orders(include_done=True)\n", "orders_all\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO [TTB_1756551786720]: Querying positions.\n", "INFO [TTB_1756551786720]: Positions queried successfully.\n"]}, {"data": {"text/plain": ["[PositionData(5070190, TXFF5, , 2025/05/29 16:38:41)]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["client.get_positions()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO [TTB_1756551786720]: Creating order for TXFF5 at price 20000 with quantity 2.\n", "INFO [TTB_1756551786720]: Order created successfully.\n"]}], "source": ["client.create_order(\n", "    \"TXFF5\",\n", "    OrderSide.SELL,\n", "    \"20000\",\n", "    TimeInForce.ROD,\n", "    OrderType.LIMIT,\n", "    \"2\",\n", "    False,\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO [TTB_3071058977728]: Querying accounts.\n", "INFO [TTB_3071058977728]: Accounts queried successfully.\n"]}, {"data": {"text/plain": ["{'CURRENCY': 'TWD',\n", " 'PREV_BALN': '2026670',\n", " 'BALN': '2025140',\n", " 'ACCT_NETV_FUTS': '2025540',\n", " 'TRAN_TAX': '430',\n", " 'TRAN_FEE': '500',\n", " 'AVAILABELMARGIN': '1651140',\n", " 'ORIGINALMARGIN': '374000',\n", " 'PREMIUM': '0'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["client.get_accounts()[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}